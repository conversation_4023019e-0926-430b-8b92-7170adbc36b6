package graphql

import (
	"context"
	"fmt"
	"net/http"
	"strings"

	"github.com/99designs/gqlgen/graphql"
	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/config"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/utils"
)

// AuthMiddleware JWT认证中间件
type AuthMiddleware struct {
	jwtConfig config.JWT
}

// NewAuthMiddleware 创建新的认证中间件
func NewAuthMiddleware(jwtConfig config.JWT) *AuthMiddleware {
	return &AuthMiddleware{
		jwtConfig: jwtConfig,
	}
}

// AuthDirective 实现@auth指令
func (m *AuthMiddleware) AuthDirective(ctx context.Context, obj interface{}, next graphql.Resolver) (interface{}, error) {
	// 从context中获取用户信息
	userID, ok := ctx.Value("userID").(uuid.UUID)
	if !ok || userID == uuid.Nil {
		return nil, fmt.Errorf("unauthorized: missing or invalid token")
	}

	// 继续执行下一个resolver
	return next(ctx)
}

// HTTPMiddleware HTTP中间件，用于从请求头中提取JWT token
func (m *AuthMiddleware) HTTPMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 从Authorization头中提取token
		authHeader := r.Header.Get("Authorization")
		if authHeader == "" {
			// 如果没有Authorization头，继续处理（可能是公开的endpoint）
			next.ServeHTTP(w, r)
			return
		}

		// 检查Bearer token格式
		if !strings.HasPrefix(authHeader, "Bearer ") {
			http.Error(w, "invalid authorization header format", http.StatusUnauthorized)
			return
		}

		// 提取token
		tokenString := strings.TrimPrefix(authHeader, "Bearer ")

		// 验证token
		claims, err := utils.ValidateJWTToken(tokenString, m.jwtConfig)
		if err != nil {
			http.Error(w, fmt.Sprintf("invalid token: %v", err), http.StatusUnauthorized)
			return
		}

		// 将用户信息添加到context中
		ctx := context.WithValue(r.Context(), "userID", claims.UserID)
		ctx = context.WithValue(ctx, "userEmail", claims.Email)
		ctx = context.WithValue(ctx, "jwtClaims", claims)

		// 继续处理请求
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// GetUserIDFromContext 从context中获取用户ID
func GetUserIDFromContext(ctx context.Context) (uuid.UUID, error) {
	userID, ok := ctx.Value("userID").(uuid.UUID)
	if !ok || userID == uuid.Nil {
		return uuid.Nil, fmt.Errorf("user ID not found in context")
	}
	return userID, nil
}

// GetUserEmailFromContext 从context中获取用户邮箱
func GetUserEmailFromContext(ctx context.Context) (string, error) {
	email, ok := ctx.Value("userEmail").(string)
	if !ok || email == "" {
		return "", fmt.Errorf("user email not found in context")
	}
	return email, nil
}

// GetJWTClaimsFromContext 从context中获取JWT声明
func GetJWTClaimsFromContext(ctx context.Context) (*utils.JWTClaims, error) {
	claims, ok := ctx.Value("jwtClaims").(*utils.JWTClaims)
	if !ok || claims == nil {
		return nil, fmt.Errorf("JWT claims not found in context")
	}
	return claims, nil
}
