package utils

import (
	"testing"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/config"
)

func TestGenerateJWTToken(t *testing.T) {
	jwtConfig := config.JWT{
		SigningKey:  "test-secret-key",
		ExpiresTime: "24h",
		BufferTime:  "1h",
		Issuer:      "xbit-agent-test",
	}

	userID := uuid.New()
	email := "<EMAIL>"

	token, err := GenerateJWTToken(userID, email, jwtConfig)
	if err != nil {
		t.Fatalf("Failed to generate JWT token: %v", err)
	}

	if token == "" {
		t.Fatal("Generated token is empty")
	}

	// 验证token
	claims, err := ValidateJWTToken(token, jwtConfig)
	if err != nil {
		t.Fatalf("Failed to validate JWT token: %v", err)
	}

	if claims.UserID != userID {
		t.Errorf("Expected user ID %v, got %v", userID, claims.UserID)
	}

	if claims.Email != email {
		t.<PERSON>rrorf("Expected email %s, got %s", email, claims.Email)
	}
}

func TestValidateJWTToken_InvalidToken(t *testing.T) {
	jwtConfig := config.JWT{
		SigningKey:  "test-secret-key",
		ExpiresTime: "24h",
		BufferTime:  "1h",
		Issuer:      "xbit-agent-test",
	}

	// 测试无效token
	_, err := ValidateJWTToken("invalid-token", jwtConfig)
	if err == nil {
		t.Fatal("Expected error for invalid token, got nil")
	}
}

func TestExtractUserIDFromToken(t *testing.T) {
	jwtConfig := config.JWT{
		SigningKey:  "test-secret-key",
		ExpiresTime: "24h",
		BufferTime:  "1h",
		Issuer:      "xbit-agent-test",
	}

	userID := uuid.New()
	email := "<EMAIL>"

	token, err := GenerateJWTToken(userID, email, jwtConfig)
	if err != nil {
		t.Fatalf("Failed to generate JWT token: %v", err)
	}

	extractedUserID, err := ExtractUserIDFromToken(token, jwtConfig)
	if err != nil {
		t.Fatalf("Failed to extract user ID from token: %v", err)
	}

	if extractedUserID != userID {
		t.Errorf("Expected user ID %v, got %v", userID, extractedUserID)
	}
}

func TestExtractEmailFromToken(t *testing.T) {
	jwtConfig := config.JWT{
		SigningKey:  "test-secret-key",
		ExpiresTime: "24h",
		BufferTime:  "1h",
		Issuer:      "xbit-agent-test",
	}

	userID := uuid.New()
	email := "<EMAIL>"

	token, err := GenerateJWTToken(userID, email, jwtConfig)
	if err != nil {
		t.Fatalf("Failed to generate JWT token: %v", err)
	}

	extractedEmail, err := ExtractEmailFromToken(token, jwtConfig)
	if err != nil {
		t.Fatalf("Failed to extract email from token: %v", err)
	}

	if extractedEmail != email {
		t.Errorf("Expected email %s, got %s", email, extractedEmail)
	}
}
